[build]
  command = "npm run build"
  publish = ".netlify/output"

[build.environment]
  NODE_VERSION = "18"

# Remove the custom functions directory - let Astro handle it
# [functions]
#   directory = "netlify/functions"
#   node_bundler = "esbuild"

# Headers for security and performance
[[headers]]
  for = "/*"
  [headers.values]
    X-Frame-Options = "DENY"
    X-Content-Type-Options = "nosniff"
    X-XSS-Protection = "1; mode=block"
    Referrer-Policy = "strict-origin-when-cross-origin"
    Permissions-Policy = "camera=(), microphone=(), geolocation=(self), payment=()"

# Cache static assets for long periods
[[headers]]
  for = "/assets/*"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"

[[headers]]
  for = "/*.js"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"

[[headers]]
  for = "/*.css"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"

[[headers]]
  for = "/*.woff2"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"

[[headers]]
  for = "/*.webp"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"

# Cache HTML for shorter time
[[headers]]
  for = "/*.html"
  [headers.values]
    Cache-Control = "public, max-age=3600, must-revalidate"

# Disable caching for API routes
[[headers]]
  for = "/api/*"
  [headers.values]
    Cache-Control = "no-cache, no-store, must-revalidate"

# Fixed redirect for Astro API routes - use 'entry' function name
[[redirects]]
  from = "/api/*"
  to = "/.netlify/functions/entry"
  status = 200

# Optional 404 page (only if you have a 404.html)
[[redirects]]
  from = "/404.html"
  to = "/404.html"
  status = 404

# Build processing settings
[build.processing]
  skip_processing = false

[build.processing.css]
  bundle = true
  minify = true

[build.processing.js]
  bundle = true
  minify = true

[build.processing.html]
  pretty_urls = true