---
// Critical CSS for above-the-fold content
---

<style is:inline>
  /* Critical styles for immediate rendering */
  
  /* Font loading optimization */
  @font-face {
    font-family: 'Allura';
    src: url('/fonts/Allura-Regular.woff2') format('woff2');
    font-display: swap;
    font-weight: normal;
    font-style: normal;
  }
  
  @font-face {
    font-family: 'Playfair Display';
    src: url('/fonts/playfairdisplay-variablefont.woff2') format('woff2');
    font-display: swap;
    font-weight: 400 900;
    font-style: normal;
  }
  
  @font-face {
    font-family: 'Roboto';
    src: url('/fonts/roboto-variable.woff2') format('woff2');
    font-display: swap;
    font-weight: 100 900;
    font-style: normal;
  }

  /* Reset and base styles */
  * {
    box-sizing: border-box;
  }
  
  html {
    scroll-behavior: smooth;
  }
  
  body {
    margin: 0;
    font-family: 'Roboto', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    line-height: 1.6;
    color: #333333;
    background-color: #ffffff;
  }

  /* Skip link for accessibility */
  .skip-link {
    position: absolute;
    top: -40px;
    left: 6px;
    background: #E8B4B8;
    color: white;
    padding: 8px 16px;
    text-decoration: none;
    border-radius: 4px;
    z-index: 1000;
    font-weight: 600;
  }
  
  .skip-link:focus {
    top: 6px;
  }

  /* Navigation critical styles */
  .nav-backdrop {
    background: rgba(248, 231, 232, 0.95);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
  }
  
  nav {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 50;
    transition: transform 0.3s ease-in-out;
  }
  
  /* Hero section critical styles */
  .hero-section {
    min-height: 100vh;
    min-height: 100dvh;
    display: flex;
    align-items: center;
    padding: 5rem 0;
    position: relative;
  }
  
  .hero-header-text {
    font-family: 'Allura', cursive;
    color: #333333;
    line-height: 0.9;
  }
  
  /* Critical layout utilities */
  .container {
    max-width: 1280px;
    margin: 0 auto;
    padding: 0 1rem;
  }
  
  .grid {
    display: grid;
  }
  
  .flex {
    display: flex;
  }
  
  .items-center {
    align-items: center;
  }
  
  .justify-between {
    justify-content: space-between;
  }
  
  .space-x-3 > * + * {
    margin-left: 0.75rem;
  }
  
  .space-y-8 > * + * {
    margin-top: 2rem;
  }
  
  /* Critical responsive utilities */
  .hidden {
    display: none;
  }
  
  .sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
  }
  
  .sr-only:focus {
    position: static;
    width: auto;
    height: auto;
    padding: inherit;
    margin: inherit;
    overflow: visible;
    clip: auto;
    white-space: normal;
  }

  /* Focus styles for accessibility */
  :focus-visible {
    outline: 2px solid #E8B4B8;
    outline-offset: 2px;
    border-radius: 4px;
  }
  
  /* Button focus styles */
  button:focus-visible,
  a:focus-visible,
  input:focus-visible,
  select:focus-visible,
  textarea:focus-visible {
    outline: 2px solid #E8B4B8;
    outline-offset: 2px;
  }

  /* Critical typography */
  .font-allura {
    font-family: 'Allura', cursive;
  }
  
  .font-playfair {
    font-family: 'Playfair Display', serif;
  }
  
  .font-roboto {
    font-family: 'Roboto', sans-serif;
  }
  
  .text-charcoal {
    color: #1a1a1a;
  }
  
  .text-dusty-rose {
    color: #E8B4B8;
  }

  /* Critical spacing */
  .pt-16 {
    padding-top: 4rem;
  }
  
  .px-4 {
    padding-left: 1rem;
    padding-right: 1rem;
  }
  
  .py-20 {
    padding-top: 5rem;
    padding-bottom: 5rem;
  }

  /* Critical responsive breakpoints */
  @media (min-width: 768px) {
    .md\\:block {
      display: block;
    }
    
    .md\\:hidden {
      display: none;
    }
    
    .md\\:grid-cols-2 {
      grid-template-columns: repeat(2, minmax(0, 1fr));
    }
    
    .md\\:text-5xl {
      font-size: 3rem;
      line-height: 1;
    }
    
    .center-mobile-text {
      text-align: left;
    }
  }
  
  @media (min-width: 1024px) {
    .lg\\:grid-cols-5 {
      grid-template-columns: repeat(5, minmax(0, 1fr));
    }
    
    .lg\\:col-span-3 {
      grid-column: span 3 / span 3;
    }
    
    .lg\\:text-7xl {
      font-size: 4.5rem;
      line-height: 1;
    }
  }
  
  @media (max-width: 767px) {
    .center-mobile-text {
      text-align: center;
    }
  }

  /* Critical animation for smooth loading */
  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
  
  .fade-in {
    animation: fadeIn 0.6s ease-out;
  }

  /* Prevent layout shift */
  img {
    max-width: 100%;
    height: auto;
  }
  
  /* Loading state styles */
  .loading {
    opacity: 0.7;
    pointer-events: none;
  }
  
  /* Critical form styles */
  input, select, textarea {
    font-family: inherit;
    font-size: 100%;
    line-height: 1.15;
    margin: 0;
  }
  
  button {
    font-family: inherit;
    font-size: 100%;
    line-height: 1.15;
    margin: 0;
    cursor: pointer;
  }
  
  button:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
</style>
