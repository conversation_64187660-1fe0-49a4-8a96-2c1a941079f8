---
export interface Props {
  src: string;
  alt: string;
  width: number;
  height: number;
  loading?: 'lazy' | 'eager';
  fetchpriority?: 'high' | 'low' | 'auto';
  class?: string;
  sizes?: string;
  quality?: number;
}

const { 
  src, 
  alt, 
  width, 
  height, 
  loading = 'lazy', 
  fetchpriority = 'auto', 
  class: className = '',
  sizes = '(max-width: 768px) 100vw, 50vw',
  quality = 80
} = Astro.props;

// Extract file info
const baseName = src.replace(/\.[^/.]+$/, "");
const ext = src.split('.').pop()?.toLowerCase();

// Generate responsive image URLs
// For now, we'll use the original images but with proper sizing
// In production, you'd generate multiple sizes
const webpSrc = src.replace(/\.(jpg|jpeg|png)$/i, '.webp');
const fallbackSrc = src;

// Calculate aspect ratio for preventing CLS
const aspectRatio = (height / width) * 100;
---

<div class={`relative ${className}`} style={`aspect-ratio: ${width}/${height};`}>
  <picture>
    <!-- WebP format for modern browsers -->
    <source 
      srcset={webpSrc}
      type="image/webp"
      sizes={sizes}
    />
    <!-- Fallback for older browsers -->
    <img
      src={fallbackSrc}
      alt={alt}
      width={width}
      height={height}
      loading={loading}
      fetchpriority={fetchpriority}
      class="w-full h-full object-cover"
      style="max-width: 100%; height: auto;"
      decoding="async"
    />
  </picture>
</div>

<style>
  /* Prevent layout shift with aspect ratio container */
  picture {
    display: block;
    width: 100%;
    height: 100%;
  }
  
  img {
    display: block;
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
</style>
